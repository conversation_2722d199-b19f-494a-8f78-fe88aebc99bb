/** @type {import('next-i18next').UserConfig} */
import FsBackend from "i18next-fs-backend";
module.exports = {
  i18n: {
    defaultLocale: "fa",
    locales: ["fa", "en"],
    localeDetection: true,
  },
  reloadOnPrerender: process.env.NODE_ENV === "development",
  ns: ["common", "auth", "dashboard"],
  defaultNS: "common",
  serializeConfig: false,
  use: [FsBackend],
  backend: {
    loadPath: "./public/locales/{{lng}}/{{ns}}.json",
  },
  react: {
    useSuspense: false,
  },
};
