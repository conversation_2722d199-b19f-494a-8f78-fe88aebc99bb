# Project Rules

* **Project context:**
  This is a **web panel** with two roles (Admin & Affiliate) sharing the same backend (NestJS).

  * Admin can manage users, products, discounts, etc.
  * Affiliate can view their commissions, reports, and referrals.
  * API docs available via `swagger.json` in root or at `http://localhost:3002/api/docs`.

* **Tech stack:**

  * TypeScript
  * Next.js 15 (App Router)
  * TailwindCSS v4
  * shadcn/ui for UI components
  * NextAuth for authentication
  * Multi-language support (i18n) with **default language = Persian**
  * NestJS backend with RBAC (roles enforced in API + frontend middleware)

* **Code quality:**

  * Follow **SOLID**, **Clean Code**, and **DRY** principles
  * Organize code into clear modules, with reusable UI components in `/shared/components`
  * Keep role-specific layouts/pages under `/admin/*` and `/affiliate/*`
  * Separate infrastructure code (Docker, configs, CI/CD) from application logic

* **Implementation rules:**

  1. Users authenticate once (NextAuth). Their roles are returned in the JWT token.
  2. Use i18n for all text, with **Persian as default** and English fallback.
  3. When generating API calls, rely on the Swagger docs.