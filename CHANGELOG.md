# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased] - Persian RTL Support

### Added
- **Persian (fa-IR) Language Support**
  - Complete i18n implementation with next-i18next
  - Persian translations for all UI strings
  - English fallback translations
  - Client-side language detection and persistence

- **Right-to-Left (RTL) Layout**
  - Global RTL direction configuration
  - RTL-aware CSS styling for all components
  - Form elements with proper text direction
  - Navigation and layout adjustments for RTL

- **Jalali Calendar Integration**
  - date-fns-jalali for Persian date formatting
  - Centralized date utility function
  - Automatic calendar detection based on locale
  - Gregorian fallback support

- **Internationalization Infrastructure**
  - next-i18next configuration
  - Translation file structure
  - Client-side i18n setup
  - Namespace-based organization

### Changed
- **Default Language**: Changed from English to Persian (fa-IR)
- **Layout Direction**: Global RTL implementation
- **Date Formatting**: Jalali calendar as default for Persian users
- **UI Strings**: All hardcoded strings replaced with i18n keys
- **Metadata**: Updated to Persian titles and descriptions

### Technical Details
- **Dependencies Added**:
  - `next-i18next`: ^15.4.2
  - `react-i18next`: ^15.7.3
  - `date-fns-jalali`: ^4.1.0-0
  - `i18next-browser-languagedetector`: ^8.2.0
  - `i18next-http-backend`: ^3.0.2

- **Files Created/Modified**:
  - `next-i18next.config.js` - i18n configuration
  - `src/lib/i18n.ts` - Client-side i18n setup
  - `src/lib/date-utils.ts` - Centralized date handling
  - `src/app/layout.tsx` - RTL and Persian metadata
  - `src/app/globals.css` - RTL styling
  - `src/app/login/page.tsx` - Localized login form
  - `src/app/dashboard/page.tsx` - Localized dashboard
  - `public/locales/fa/*.json` - Persian translations
  - `public/locales/en/*.json` - English translations
  - `MIGRATION.md` - Migration guide

### Migration Notes
- See `MIGRATION.md` for detailed migration instructions
- Breaking changes include default language and layout direction
- Authentication flows preserved with localized messages

### Example Persian UI Strings
- **Login Page**: "ورود مدیر" (Admin Login)
- **Dashboard**: "پنل مدیریت" (Admin Panel)
- **Navigation**: "مدیریت کاربران" (User Moderation)
- **Errors**: "اطلاعات کاربری نامعتبر" (Invalid credentials)

### Accessibility
- RTL layout maintains proper semantic structure
- Screen reader compatibility preserved
- Keyboard navigation works in RTL context
- Form labels and ARIA attributes updated

### Browser Support
- Modern browsers with RTL support
- Tested on Chrome, Firefox, Safari
- Mobile responsive with RTL considerations