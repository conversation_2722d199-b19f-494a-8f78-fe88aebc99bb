// Authentication DTOs based on Swagger API documentation

export interface CheckRegisterPhoneInputDto {
  country_name: string;
  country_code: string;
  phone: string;
}

export interface CheckPhoneRegisteredOutputDto {
  status: "SendEmail" | "SendSms" | "GetEmail";
  email: string;
}

export interface UserLoginInputDto {
  country_code: string;
  phone: string;
  confirmation_code: string;
  device_id: string;
}

export interface UserLoginOutputDto {
  access_token: string;
  refresh_token: string;
}

export interface AuthError {
  message: string;
  status?: number;
}

// Constants
export const IRAN_COUNTRY_CODE = "+98";
export const IRAN_COUNTRY_NAME = "IR";
