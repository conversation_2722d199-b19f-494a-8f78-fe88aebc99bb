import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";
import {
  UserLoginInputDto,
  UserLoginOutputDto,
  IRAN_COUNTRY_CODE,
} from "./auth-types";

declare module "next-auth" {
  interface User {
    accessToken?: string;
    refreshToken?: string;
  }
  interface Session {
    accessToken?: string;
    refreshToken?: string;
  }
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
  }
}

export const { handlers, auth, signIn, signOut } = NextAuth({
  trustHost: true, // Required for development and proxy environments
  basePath: "/api/auth", // Explicitly set base path for API routes
  secret: process.env.NEXTAUTH_SECRET || "fallback-secret-for-development", // Use environment variable with fallback
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        phone: { label: "Phone", type: "text" },
        confirmation_code: { label: "OTP Code", type: "text" },
        device_id: { label: "Device ID", type: "text" },
      },
      async authorize(credentials) {
        if (
          !credentials?.phone ||
          !credentials?.confirmation_code ||
          !credentials?.device_id
        ) {
          return null;
        }

        const phone = credentials.phone as string;
        const confirmationCode = credentials.confirmation_code as string;
        const deviceId = credentials.device_id as string;

        try {
          // Get API base URL from environment variable
          const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

          // Proceed directly with login (phone registration check is handled in the frontend)
          const loginData: UserLoginInputDto = {
            country_code: IRAN_COUNTRY_CODE,
            phone: phone,
            confirmation_code: confirmationCode,
            device_id: deviceId,
          };

          console.log("Login API call:", `${apiBaseUrl}/auth/login`);
          console.log("Login data:", loginData);

          const loginResponse = await fetch(`${apiBaseUrl}/auth/login`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(loginData),
            // Add timeout and other fetch options for better error handling
            signal: AbortSignal.timeout(10000), // 10 second timeout
          });

          console.log("Login response status:", loginResponse.status);

          if (!loginResponse.ok) {
            const errorText = await loginResponse.text();
            console.error("Login failed:", loginResponse.status, errorText);
            return null;
          }

          const loginResult: UserLoginOutputDto = await loginResponse.json();

          // For admin panel, we might need to get user profile to get user details
          // But for now, return with tokens
          return {
            id: phone, // Use phone as ID since we don't have user ID yet
            accessToken: loginResult.access_token,
            refreshToken: loginResult.refresh_token,
          };
        } catch (error) {
          console.error("Auth error:", error);

          // Handle different types of errors
          if (error instanceof Error) {
            if (error.name === "AbortError") {
              console.error("Request timeout - API server may be unreachable");
            } else if (error.message.includes("fetch")) {
              console.error("Network error - check API server connectivity");
            }
          }

          return null;
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
    updateAge: 60 * 60, // Update session every hour
  },
  // Add additional configuration for better session handling
  useSecureCookies: process.env.NODE_ENV === "production",
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
  events: {
    async signIn(message) {
      console.log("🔐 SignIn event:", message);
    },
    async signOut(message) {
      console.log("🔐 SignOut event:", message);
    },
    async session(message) {
      console.log("🔐 Session event:", message);
    },
  },
  callbacks: {
    async jwt({ token, user, trigger }) {
      console.log("JWT callback triggered:", {
        trigger,
        hasToken: !!token,
        hasUser: !!user,
        tokenKeys: token ? Object.keys(token) : null,
        userKeys: user ? Object.keys(user) : null,
      });

      if (user) {
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;
        console.log("JWT token updated with user data:", {
          accessToken: !!user.accessToken,
          refreshToken: !!user.refreshToken,
        });
      }

      return token;
    },
    async session({ session, token }) {
      console.log("Session callback triggered:", {
        hasSession: !!session,
        hasToken: !!token,
        tokenKeys: token ? Object.keys(token) : null,
      });

      session.accessToken = token.accessToken as string;
      session.refreshToken = token.refreshToken as string;

      console.log("Session updated with token data:", {
        accessToken: !!session.accessToken,
        refreshToken: !!session.refreshToken,
      });

      return session;
    },
    async signIn({ user, account, profile }) {
      console.log("🔐 SignIn callback triggered:", {
        hasUser: !!user,
        hasAccount: !!account,
        hasProfile: !!profile,
        userId: user?.id,
        accountProvider: account?.provider,
      });

      return true; // Allow sign in
    },
  },
  pages: {
    signIn: "/login",
  },
  debug: process.env.NODE_ENV === "development",
  logger: {
    error: (error: Error, ...args: unknown[]) => {
      console.error("NextAuth Error:", error.message, ...args);
    },
    warn: (message: string) => {
      console.warn("NextAuth Warning:", message);
    },
  },
});
