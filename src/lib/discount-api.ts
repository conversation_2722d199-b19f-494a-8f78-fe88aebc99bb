import { DiscountInputDto, DiscountOutputDto, CreateDiscountResponse, ApiError } from "./discount-types";

/**
 * Create a new discount code
 * @param discountData - The discount data to create
 * @param accessToken - The user's access token for authentication
 * @returns Promise with the created discount or error
 */
export async function createDiscount(
  discountData: DiscountInputDto,
  accessToken: string
): Promise<CreateDiscountResponse> {
  try {
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    
    if (!apiBaseUrl) {
      throw new Error("API base URL is not configured");
    }

    console.log("Creating discount with data:", discountData);

    const response = await fetch(`${apiBaseUrl}/discounts/create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${accessToken}`,
      },
      body: JSON.stringify(discountData),
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    console.log("Create discount response status:", response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Create discount failed:", response.status, errorText);
      
      let errorMessage = "خطا در ایجاد کد تخفیف";
      
      try {
        const errorData: ApiError = JSON.parse(errorText);
        errorMessage = errorData.message || errorMessage;
      } catch {
        // If parsing fails, use default message
      }

      return {
        success: false,
        message: errorMessage,
      };
    }

    const result: DiscountOutputDto = await response.json();
    console.log("Discount created successfully:", result);

    return {
      success: true,
      message: "کد تخفیف با موفقیت ایجاد شد",
      data: result,
    };
  } catch (error) {
    console.error("Create discount error:", error);

    let errorMessage = "خطا در ایجاد کد تخفیف";
    
    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Get all discount codes
 * @param accessToken - The user's access token for authentication
 * @param state - Optional filter by state
 * @returns Promise with the discount list or error
 */
export async function getDiscounts(
  accessToken: string,
  state?: "ACTIVE" | "INACTIVE"
): Promise<{ success: boolean; data?: DiscountOutputDto[]; message?: string }> {
  try {
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    
    if (!apiBaseUrl) {
      throw new Error("API base URL is not configured");
    }

    const url = new URL(`${apiBaseUrl}/discounts/all`);
    if (state) {
      url.searchParams.append("state", state);
    }

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
      },
      signal: AbortSignal.timeout(10000),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Get discounts failed:", response.status, errorText);
      
      return {
        success: false,
        message: "خطا در دریافت لیست کدهای تخفیف",
      };
    }

    const result: DiscountOutputDto[] = await response.json();
    
    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error("Get discounts error:", error);
    
    return {
      success: false,
      message: "خطا در دریافت لیست کدهای تخفیف",
    };
  }
}
