// Discount DTOs based on Swagger API documentation

export interface DiscountInputDto {
  expire_date: string; // YYYY-MM-DD format
  state: "ACTIVE" | "INACTIVE";
  discount_percent: number;
  count: number;
  type: "simple" | "referral";
  referral_id: number;
  description?: string;
}

export interface DiscountOutputDto {
  id: number;
  code: string;
  discount_percent: number;
  expire_date: string; // ISO date-time format
  state: "ACTIVE" | "INACTIVE";
  type: "simple" | "referral";
  referral_id: number;
  description?: string;
}

export interface CreateDiscountResponse {
  success: boolean;
  message: string;
  data?: DiscountOutputDto;
}

export interface DiscountFormData {
  expireDate: string;
  state: "ACTIVE" | "INACTIVE";
  discountPercent: number;
  count: number;
  type: "simple" | "referral";
  referralId: number;
  description?: string;
}

// Form validation types
export interface DiscountFormErrors {
  expireDate?: string;
  state?: string;
  discountPercent?: string;
  count?: string;
  type?: string;
  referralId?: string;
  description?: string;
}

// API response types
export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
}
