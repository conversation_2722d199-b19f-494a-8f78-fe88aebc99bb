import i18n from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";

// Initialize i18n without react-i18next for now to avoid React 19 compatibility issues
i18n
  .use(Backend)
  .use(LanguageDetector)
  .init({
    fallbackLng: "fa",
    lng: "fa",
    debug: process.env.NODE_ENV === "development",

    interpolation: {
      escapeValue: false,
    },

    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },

    detection: {
      order: ["localStorage", "navigator", "htmlTag"],
      caches: ["localStorage"],
    },

    ns: ["common", "auth", "dashboard"],
    defaultNS: "common",
  });

export default i18n;

// Simple translation function for React 19 compatibility
export const t = (key: string, ns?: string): string => {
  const namespace = ns || "common";
  const currentLang = i18n.language || "fa";

  // For now, return key if translation not loaded
  // In production, this would be replaced with proper translation loading
  return key;
};
