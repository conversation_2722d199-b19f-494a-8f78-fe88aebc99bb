"use client";

import { useSession } from "next-auth/react";
import { useRouter, usePathname } from "next/navigation";
import { useEffect, ReactNode } from "react";

// Simple translation function for React 19 compatibility
const t = (key: string): string => {
  const translations: Record<string, string> = {
    "auth:loading": "در حال بارگذاری...",
    "auth:login.pleaseLogIn": "لطفاً وارد شوید",
    "auth:login.accessDenied": "دسترسی غیرمجاز",
    "auth:login.sessionExpired": "نشست شما منقضی شده است",
  };
  return translations[key] || key;
};

interface AuthGuardProps {
  children: ReactNode;
  requireAdmin?: boolean;
  fallback?: ReactNode;
  redirectTo?: string;
}

export default function AuthGuard({
  children,
  requireAdmin = false,
  fallback,
  redirectTo = "/login",
}: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  // Debug logging
  console.log("AuthGuard - Render:", {
    pathname,
    status,
    hasSession: !!session,
    sessionKeys: session ? Object.keys(session) : null,
  });

  useEffect(() => {
    console.log("AuthGuard effect:", { status, session: !!session, pathname });

    if (status === "loading") return; // Still loading

    if (!session) {
      // User is not authenticated
      console.log("No session found, redirecting to login");
      const loginUrl = new URL(redirectTo, window.location.origin);
      loginUrl.searchParams.set("returnUrl", pathname);
      router.push(loginUrl.toString());
      return;
    }

    console.log("Session found, user authenticated");

    // Check for admin role if required
    if (requireAdmin) {
      const enableRoleCheck =
        process.env.NEXT_PUBLIC_ENABLE_ADMIN_ROLE_CHECK === "true";
      if (enableRoleCheck) {
        // In a real implementation, you'd check user roles from the session
        // For now, we assume all authenticated users are admins
        // const userRole = session.user?.role;
        // if (userRole !== 'ADMIN' && userRole !== 'MASTER') {
        //   router.push('/403');
        //   return;
        // }
      }
    }

    // Check for token expiration
    if (session.expires && new Date(session.expires) < new Date()) {
      const loginUrl = new URL(redirectTo, window.location.origin);
      loginUrl.searchParams.set("returnUrl", pathname);
      loginUrl.searchParams.set("expired", "true");
      router.push(loginUrl.toString());
      return;
    }
  }, [session, status, router, pathname, requireAdmin, redirectTo]);

  // Show loading state
  if (status === "loading") {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t("auth:loading")}</p>
          </div>
        </div>
      )
    );
  }

  // Show access denied for unauthenticated users
  if (!session) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {t("auth:login.accessDenied")}
            </h1>
            <p className="text-gray-600">{t("auth:login.pleaseLogIn")}</p>
          </div>
        </div>
      )
    );
  }

  // Check for expired session
  if (session.expires && new Date(session.expires) < new Date()) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {t("auth:login.sessionExpired")}
            </h1>
            <p className="text-gray-600">{t("auth:login.pleaseLogIn")}</p>
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
}

// Higher-order component for protecting pages
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<AuthGuardProps, "children"> = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <AuthGuard {...options}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}
