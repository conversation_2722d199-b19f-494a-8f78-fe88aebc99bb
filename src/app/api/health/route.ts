import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Check if the external API is accessible
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    if (!apiBaseUrl) {
      return NextResponse.json(
        {
          status: "error",
          message: "API_BASE_URL not configured",
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }

    // Try to reach the external API
    const response = await fetch(`${apiBaseUrl}/`, {
      method: "GET",
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });

    return NextResponse.json({
      status: "ok",
      message: "Health check passed",
      apiAccessible: response.ok,
      apiStatus: response.status,
      timestamp: new Date().toISOString(),
      config: {
        apiBaseUrl,
        nextAuthUrl: process.env.NEXTAUTH_URL,
        nodeEnv: process.env.NODE_ENV,
      },
    });
  } catch (error) {
    console.error("Health check failed:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Health check failed",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
