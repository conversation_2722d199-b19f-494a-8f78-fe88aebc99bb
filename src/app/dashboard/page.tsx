"use client";

import { useSession, signOut } from "next-auth/react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { formatDate } from "@/lib/date-utils";
import AuthGuard from "@/components/auth-guard";

// Simple translation function for React 19 compatibility
const t = (key: string): string => {
  const translations: Record<string, string> = {
    "dashboard:title": "پنل مدیریت",
    date: "تاریخ",
    "auth:logout": "خروج",
    "dashboard:navigation.dashboard": "داشبورد",
    "dashboard:navigation.userModeration": "مدیریت کاربران",
    "dashboard:navigation.productManagement": "مدیریت محصولات",
    "dashboard:navigation.salesReports": "گزارش فروش",
    "dashboard:navigation.discountCodes": "کدهای تخفیف",
    "dashboard:cards.totalUsers.title": "کل کاربران",
    "dashboard:cards.totalUsers.description": "کاربران فعال در سیستم",
    "dashboard:cards.products.title": "محصولات",
    "dashboard:cards.products.description": "آیتم‌های موجود در کاتالوگ",
    "dashboard:cards.sales.title": "فروش",
    "dashboard:cards.sales.description": "درآمد این ماه",
    "dashboard:cards.discounts.title": "تخفیف‌ها",
    "dashboard:cards.discounts.description": "کدهای تخفیف فعال",
  };
  return translations[key] || key;
};

function DashboardContent() {
  const { data: session } = useSession();

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {t("dashboard:title")}
              </h1>
              <p className="text-sm text-gray-600">
                {t("date")}: {formatDate(new Date())}
              </p>
            </div>
            <Button onClick={() => signOut()} variant="outline">
              {t("auth:logout")}
            </Button>
          </div>
        </div>
      </header>

      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 py-4">
            <a href="#" className="text-gray-900 hover:text-blue-600">
              {t("dashboard:navigation.dashboard")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.userModeration")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.productManagement")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.salesReports")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.discountCodes")}
            </a>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>{t("dashboard:cards.totalUsers.title")}</CardTitle>
                <CardDescription>
                  {t("dashboard:cards.totalUsers.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">۱,۲۳۴</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>{t("dashboard:cards.products.title")}</CardTitle>
                <CardDescription>
                  {t("dashboard:cards.products.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">۵۶۷</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>{t("dashboard:cards.sales.title")}</CardTitle>
                <CardDescription>
                  {t("dashboard:cards.sales.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">۸۹,۴۳۲ تومان</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>{t("dashboard:cards.discounts.title")}</CardTitle>
                <CardDescription>
                  {t("dashboard:cards.discounts.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">۲۳</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <AuthGuard requireAdmin={true}>
      <DashboardContent />
    </AuthGuard>
  );
}
