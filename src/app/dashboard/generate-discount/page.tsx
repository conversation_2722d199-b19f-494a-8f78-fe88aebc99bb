"use client";

import { useState } from "react";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { formatDate } from "@/lib/date-utils";
import AuthGuard from "@/components/auth-guard";
import { createDiscount } from "@/lib/discount-api";
import { DiscountFormData, DiscountFormErrors } from "@/lib/discount-types";

// Simple translation function for React 19 compatibility
const t = (key: string): string => {
  const translations: Record<string, string> = {
    "dashboard:title": "پنل مدیریت",
    "dashboard:discountGeneration.title": "ایجاد کد تخفیف",
    "dashboard:discountGeneration.subtitle": "کد تخفیف جدید ایجاد کنید",
    "dashboard:discountGeneration.form.expireDate.label": "تاریخ انقضا",
    "dashboard:discountGeneration.form.expireDate.placeholder": "YYYY-MM-DD",
    "dashboard:discountGeneration.form.state.label": "وضعیت",
    "dashboard:discountGeneration.form.state.placeholder":
      "وضعیت کد تخفیف را انتخاب کنید",
    "dashboard:discountGeneration.form.state.options.ACTIVE": "فعال",
    "dashboard:discountGeneration.form.state.options.INACTIVE": "غیرفعال",
    "dashboard:discountGeneration.form.discountPercent.label": "درصد تخفیف",
    "dashboard:discountGeneration.form.discountPercent.placeholder":
      "درصد تخفیف را وارد کنید",
    "dashboard:discountGeneration.form.count.label": "تعداد تولید",
    "dashboard:discountGeneration.form.count.placeholder": "تعداد کد تخفیف",
    "dashboard:discountGeneration.form.type.label": "نوع کد تخفیف",
    "dashboard:discountGeneration.form.type.placeholder":
      "نوع کد تخفیف را انتخاب کنید",
    "dashboard:discountGeneration.form.type.options.simple": "ساده",
    "dashboard:discountGeneration.form.type.options.referral": "همکاری",
    "dashboard:discountGeneration.form.referralId.label": "شناسه همکار",
    "dashboard:discountGeneration.form.referralId.placeholder":
      "شناسه همکار را وارد کنید",
    "dashboard:discountGeneration.form.description.label": "توضیحات",
    "dashboard:discountGeneration.form.description.placeholder":
      "توضیحات کد تخفیف را وارد کنید",
    "dashboard:discountGeneration.buttons.submit": "ایجاد کد تخفیف",
    "dashboard:discountGeneration.buttons.cancel": "انصراف",
    "dashboard:discountGeneration.buttons.reset": "پاک کردن فرم",
    "dashboard:discountGeneration.validation.required": "این فیلد الزامی است",
    "dashboard:discountGeneration.validation.invalidDate":
      "تاریخ وارد شده معتبر نیست",
    "dashboard:discountGeneration.validation.invalidPercent":
      "درصد تخفیف باید بین ۱ تا ۱۰۰ باشد",
    "dashboard:discountGeneration.validation.invalidCount":
      "تعداد تولید باید عدد مثبت باشد",
    "dashboard:discountGeneration.validation.invalidReferralId":
      "برای کدهای همکاری، شناسه همکار الزامی است",
    "dashboard:discountGeneration.messages.success":
      "کد تخفیف با موفقیت ایجاد شد",
    "dashboard:discountGeneration.messages.error": "خطا در ایجاد کد تخفیف",
    "dashboard:discountGeneration.messages.loading": "در حال ایجاد کد تخفیف...",
    "dashboard:navigation.dashboard": "داشبورد",
    "dashboard:navigation.userModeration": "مدیریت کاربران",
    "dashboard:navigation.productManagement": "مدیریت محصولات",
    "dashboard:navigation.salesReports": "گزارش فروش",
    "dashboard:navigation.discountCodes": "کدهای تخفیف",
    "dashboard:navigation.generateDiscount": "ایجاد کد تخفیف",
    "auth:logout": "خروج",
    date: "تاریخ",
  };
  return translations[key] || key;
};

function GenerateDiscountContent() {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState<"success" | "error" | "">("");

  const [formData, setFormData] = useState<DiscountFormData>({
    expireDate: "",
    state: "ACTIVE",
    discountPercent: 0,
    count: 1,
    type: "simple",
    referralId: 0,
    description: "",
  });

  const [errors, setErrors] = useState<DiscountFormErrors>({});

  const validateForm = (): boolean => {
    const newErrors: DiscountFormErrors = {};

    // Validate expire date
    if (!formData.expireDate) {
      newErrors.expireDate = t(
        "dashboard:discountGeneration.validation.required"
      );
    } else {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(formData.expireDate)) {
        newErrors.expireDate = t(
          "dashboard:discountGeneration.validation.invalidDate"
        );
      }
    }

    // Validate state
    if (!formData.state) {
      newErrors.state = t("dashboard:discountGeneration.validation.required");
    }

    // Validate discount percent
    if (
      !formData.discountPercent ||
      formData.discountPercent < 1 ||
      formData.discountPercent > 100
    ) {
      newErrors.discountPercent = t(
        "dashboard:discountGeneration.validation.invalidPercent"
      );
    }

    // Validate count
    if (!formData.count || formData.count < 1) {
      newErrors.count = t(
        "dashboard:discountGeneration.validation.invalidCount"
      );
    }

    // Validate type
    if (!formData.type) {
      newErrors.type = t("dashboard:discountGeneration.validation.required");
    }

    // Validate referral ID for referral type
    if (
      formData.type === "referral" &&
      (!formData.referralId || formData.referralId < 1)
    ) {
      newErrors.referralId = t(
        "dashboard:discountGeneration.validation.invalidReferralId"
      );
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!session?.accessToken) {
      setMessage("خطا در احراز هویت");
      setMessageType("error");
      return;
    }

    setLoading(true);
    setMessage("");
    setMessageType("");

    try {
      const discountData = {
        expire_date: formData.expireDate,
        state: formData.state,
        discount_percent: formData.discountPercent,
        count: formData.count,
        type: formData.type,
        referral_id: formData.referralId,
        description: formData.description,
      };

      const result = await createDiscount(discountData, session.accessToken);

      if (result.success) {
        setMessage(result.message);
        setMessageType("success");
        // Reset form
        setFormData({
          expireDate: "",
          state: "ACTIVE",
          discountPercent: 0,
          count: 1,
          type: "simple",
          referralId: 0,
          description: "",
        });
        setErrors({});
      } else {
        setMessage(result.message);
        setMessageType("error");
      }
    } catch (error) {
      console.error("Submit error:", error);
      setMessage(t("dashboard:discountGeneration.messages.error"));
      setMessageType("error");
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setFormData({
      expireDate: "",
      state: "ACTIVE",
      discountPercent: 0,
      count: 1,
      type: "simple",
      referralId: 0,
      description: "",
    });
    setErrors({});
    setMessage("");
    setMessageType("");
  };

  const handleCancel = () => {
    router.push("/dashboard");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {t("dashboard:title")}
              </h1>
              <p className="text-sm text-gray-600">
                {t("date")}: {formatDate(new Date())}
              </p>
            </div>
            <Button onClick={() => signOut()} variant="outline">
              {t("auth:logout")}
            </Button>
          </div>
        </div>
      </header>

      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 py-4">
            <a href="/dashboard" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.dashboard")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.userModeration")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.productManagement")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.salesReports")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.discountCodes")}
            </a>
            <a
              href="/dashboard/generate-discount"
              className="text-gray-900 hover:text-blue-600"
            >
              {t("dashboard:navigation.generateDiscount")}
            </a>
          </div>
        </div>
      </nav>

      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <Card>
            <CardHeader>
              <CardTitle>{t("dashboard:discountGeneration.title")}</CardTitle>
              <CardDescription>
                {t("dashboard:discountGeneration.subtitle")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {message && (
                <div
                  className={`mb-4 p-3 rounded-md text-sm ${
                    messageType === "success"
                      ? "bg-green-50 text-green-800 border border-green-200"
                      : "bg-red-50 text-red-800 border border-red-200"
                  }`}
                >
                  {message}
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Expire Date */}
                  <div className="space-y-2">
                    <Label htmlFor="expireDate">
                      {t("dashboard:discountGeneration.form.expireDate.label")}
                    </Label>
                    <Input
                      id="expireDate"
                      type="date"
                      value={formData.expireDate}
                      onChange={(e) =>
                        setFormData({ ...formData, expireDate: e.target.value })
                      }
                      placeholder={t(
                        "dashboard:discountGeneration.form.expireDate.placeholder"
                      )}
                      className={errors.expireDate ? "border-red-500" : ""}
                    />
                    {errors.expireDate && (
                      <p className="text-red-600 text-sm">
                        {errors.expireDate}
                      </p>
                    )}
                  </div>

                  {/* State */}
                  <div className="space-y-2">
                    <Label htmlFor="state">
                      {t("dashboard:discountGeneration.form.state.label")}
                    </Label>
                    <Select
                      id="state"
                      value={formData.state}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          state: e.target.value as "ACTIVE" | "INACTIVE",
                        })
                      }
                      placeholder={t(
                        "dashboard:discountGeneration.form.state.placeholder"
                      )}
                      className={errors.state ? "border-red-500" : ""}
                    >
                      <option value="ACTIVE">
                        {t(
                          "dashboard:discountGeneration.form.state.options.ACTIVE"
                        )}
                      </option>
                      <option value="INACTIVE">
                        {t(
                          "dashboard:discountGeneration.form.state.options.INACTIVE"
                        )}
                      </option>
                    </Select>
                    {errors.state && (
                      <p className="text-red-600 text-sm">{errors.state}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Discount Percent */}
                  <div className="space-y-2">
                    <Label htmlFor="discountPercent">
                      {t(
                        "dashboard:discountGeneration.form.discountPercent.label"
                      )}
                    </Label>
                    <Input
                      id="discountPercent"
                      type="number"
                      min="1"
                      max="100"
                      value={formData.discountPercent || ""}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          discountPercent: parseInt(e.target.value) || 0,
                        })
                      }
                      placeholder={t(
                        "dashboard:discountGeneration.form.discountPercent.placeholder"
                      )}
                      className={errors.discountPercent ? "border-red-500" : ""}
                    />
                    {errors.discountPercent && (
                      <p className="text-red-600 text-sm">
                        {errors.discountPercent}
                      </p>
                    )}
                  </div>

                  {/* Count */}
                  <div className="space-y-2">
                    <Label htmlFor="count">
                      {t("dashboard:discountGeneration.form.count.label")}
                    </Label>
                    <Input
                      id="count"
                      type="number"
                      min="1"
                      value={formData.count || ""}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          count: parseInt(e.target.value) || 1,
                        })
                      }
                      placeholder={t(
                        "dashboard:discountGeneration.form.count.placeholder"
                      )}
                      className={errors.count ? "border-red-500" : ""}
                    />
                    {errors.count && (
                      <p className="text-red-600 text-sm">{errors.count}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Type */}
                  <div className="space-y-2">
                    <Label htmlFor="type">
                      {t("dashboard:discountGeneration.form.type.label")}
                    </Label>
                    <Select
                      id="type"
                      value={formData.type}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          type: e.target.value as "simple" | "referral",
                        })
                      }
                      placeholder={t(
                        "dashboard:discountGeneration.form.type.placeholder"
                      )}
                      className={errors.type ? "border-red-500" : ""}
                    >
                      <option value="simple">
                        {t(
                          "dashboard:discountGeneration.form.type.options.simple"
                        )}
                      </option>
                      <option value="referral">
                        {t(
                          "dashboard:discountGeneration.form.type.options.referral"
                        )}
                      </option>
                    </Select>
                    {errors.type && (
                      <p className="text-red-600 text-sm">{errors.type}</p>
                    )}
                  </div>

                  {/* Referral ID */}
                  <div className="space-y-2">
                    <Label htmlFor="referralId">
                      {t("dashboard:discountGeneration.form.referralId.label")}
                    </Label>
                    <Input
                      id="referralId"
                      type="number"
                      min="0"
                      value={formData.referralId || ""}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          referralId: parseInt(e.target.value) || 0,
                        })
                      }
                      placeholder={t(
                        "dashboard:discountGeneration.form.referralId.placeholder"
                      )}
                      className={errors.referralId ? "border-red-500" : ""}
                      disabled={formData.type === "simple"}
                    />
                    {errors.referralId && (
                      <p className="text-red-600 text-sm">
                        {errors.referralId}
                      </p>
                    )}
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description">
                    {t("dashboard:discountGeneration.form.description.label")}
                  </Label>
                  <Input
                    id="description"
                    type="text"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    placeholder={t(
                      "dashboard:discountGeneration.form.description.placeholder"
                    )}
                    className={errors.description ? "border-red-500" : ""}
                  />
                  {errors.description && (
                    <p className="text-red-600 text-sm">{errors.description}</p>
                  )}
                </div>

                {/* Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Button type="submit" disabled={loading} className="flex-1">
                    {loading
                      ? t("dashboard:discountGeneration.messages.loading")
                      : t("dashboard:discountGeneration.buttons.submit")}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleReset}
                    disabled={loading}
                  >
                    {t("dashboard:discountGeneration.buttons.reset")}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={loading}
                  >
                    {t("dashboard:discountGeneration.buttons.cancel")}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}

export default function GenerateDiscountPage() {
  return (
    <AuthGuard requireAdmin={true}>
      <GenerateDiscountContent />
    </AuthGuard>
  );
}
