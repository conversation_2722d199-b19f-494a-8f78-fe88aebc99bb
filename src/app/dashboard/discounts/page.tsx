"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate } from "@/lib/date-utils";
import AuthGuard from "@/components/auth-guard";
import { getDiscounts } from "@/lib/discount-api";
import { DiscountOutputDto } from "@/lib/discount-types";

// Simple translation function for React 19 compatibility
const t = (key: string): string => {
  const translations: Record<string, string> = {
    "dashboard:title": "پنل مدیریت",
    "dashboard:discountList.title": "لیست کدهای تخفیف",
    "dashboard:discountList.subtitle": "مشاهده و مدیریت تمام کدهای تخفیف",
    "dashboard:discountList.filters.all": "همه",
    "dashboard:discountList.filters.active": "فعال",
    "dashboard:discountList.filters.inactive": "غیرفعال",
    "dashboard:discountList.filters.filterByStatus": "فیلتر بر اساس وضعیت",
    "dashboard:discountList.table.headers.code": "کد تخفیف",
    "dashboard:discountList.table.headers.percentage": "درصد تخفیف",
    "dashboard:discountList.table.headers.expiry": "تاریخ انقضا",
    "dashboard:discountList.table.headers.status": "وضعیت",
    "dashboard:discountList.table.headers.type": "نوع",
    "dashboard:discountList.table.headers.count": "تعداد استفاده",
    "dashboard:discountList.table.headers.description": "توضیحات",
    "dashboard:discountList.table.status.ACTIVE": "فعال",
    "dashboard:discountList.table.status.DEACTIVE": "غیرفعال",
    "dashboard:discountList.table.status.USED": "استفاده شده",
    "dashboard:discountList.table.type.simple": "ساده",
    "dashboard:discountList.table.type.referral": "همکاری",
    "dashboard:discountList.table.noData": "هیچ کد تخفیفی یافت نشد",
    "dashboard:discountList.table.noDataDescription":
      "در حال حاضر کد تخفیفی در سیستم وجود ندارد",
    "dashboard:discountList.actions.refresh": "بروزرسانی",
    "dashboard:discountList.actions.createNew": "ایجاد کد جدید",
    "dashboard:discountList.messages.loading": "در حال بارگذاری کدهای تخفیف...",
    "dashboard:discountList.messages.error": "خطا در بارگذاری کدهای تخفیف",
    "dashboard:discountList.messages.loadError":
      "امکان بارگذاری اطلاعات وجود ندارد. لطفاً دوباره تلاش کنید.",
    "dashboard:discountList.messages.networkError": "خطا در اتصال به سرور",
    "dashboard:discountList.stats.total": "کل کدها",
    "dashboard:discountList.stats.active": "فعال",
    "dashboard:discountList.stats.inactive": "غیرفعال",
    "dashboard:discountList.stats.expired": "منقضی شده",
    "dashboard:navigation.dashboard": "داشبورد",
    "dashboard:navigation.userModeration": "مدیریت کاربران",
    "dashboard:navigation.productManagement": "مدیریت محصولات",
    "dashboard:navigation.salesReports": "گزارش فروش",
    "dashboard:navigation.discountCodes": "کدهای تخفیف",
    "dashboard:navigation.generateDiscount": "ایجاد کد تخفیف",
    "auth:logout": "خروج",
    date: "تاریخ",
  };
  return translations[key] || key;
};

type FilterStatus = "all" | "ACTIVE" | "INACTIVE";

function DiscountListContent() {
  const { data: session } = useSession();
  const router = useRouter();
  const [discounts, setDiscounts] = useState<DiscountOutputDto[]>([]);
  const [filteredDiscounts, setFilteredDiscounts] = useState<
    DiscountOutputDto[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [filter, setFilter] = useState<FilterStatus>("all");

  const loadDiscounts = useCallback(async () => {
    if (!session?.accessToken) {
      setError("خطا در احراز هویت");
      setLoading(false);
      return;
    }

    setLoading(true);
    setError("");

    try {
      const result = await getDiscounts(session.accessToken);

      if (result.success && result.data) {
        setDiscounts(result.data);
        setFilteredDiscounts(result.data);
      } else {
        setError(result.message || t("dashboard:discountList.messages.error"));
      }
    } catch (err) {
      console.error("Load discounts error:", err);
      setError(t("dashboard:discountList.messages.networkError"));
    } finally {
      setLoading(false);
    }
  }, [session?.accessToken]);

  useEffect(() => {
    loadDiscounts();
  }, [loadDiscounts]);

  useEffect(() => {
    if (filter === "all") {
      setFilteredDiscounts(discounts);
    } else if (filter === "ACTIVE") {
      setFilteredDiscounts(
        discounts.filter((discount) => discount.state === "ACTIVE")
      );
    } else if (filter === "INACTIVE") {
      setFilteredDiscounts(
        discounts.filter(
          (discount) =>
            discount.state === "DEACTIVE" || discount.state === "USED"
        )
      );
    }
  }, [filter, discounts]);

  const handleRefresh = () => {
    loadDiscounts();
  };

  const handleCreateNew = () => {
    router.push("/dashboard/generate-discount");
  };

  const getStats = () => {
    const total = discounts.length;
    const active = discounts.filter((d) => d.state === "ACTIVE").length;
    const inactive = discounts.filter(
      (d) => d.state === "DEACTIVE" || d.state === "USED"
    ).length;
    const now = new Date();
    const expired = discounts.filter(
      (d) => new Date(d.expire_date) < now
    ).length;

    return { total, active, inactive, expired };
  };

  const stats = getStats();

  const isExpired = (expireDate: string) => {
    return new Date(expireDate) < new Date();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {t("dashboard:title")}
              </h1>
              <p className="text-sm text-gray-600">
                {t("date")}: {formatDate(new Date())}
              </p>
            </div>
            <Button onClick={() => signOut()} variant="outline">
              {t("auth:logout")}
            </Button>
          </div>
        </div>
      </header>

      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 py-4">
            <a href="/dashboard" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.dashboard")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.userModeration")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.productManagement")}
            </a>
            <a href="#" className="text-gray-500 hover:text-blue-600">
              {t("dashboard:navigation.salesReports")}
            </a>
            <a
              href="/dashboard/discounts"
              className="text-gray-900 hover:text-blue-600"
            >
              {t("dashboard:navigation.discountCodes")}
            </a>
            <a
              href="/dashboard/generate-discount"
              className="text-gray-500 hover:text-blue-600"
            >
              {t("dashboard:navigation.generateDiscount")}
            </a>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Page Header */}
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              {t("dashboard:discountList.title")}
            </h2>
            <p className="text-gray-600">
              {t("dashboard:discountList.subtitle")}
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle>{t("dashboard:discountList.stats.total")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>
                  {t("dashboard:discountList.stats.active")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {stats.active}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>
                  {t("dashboard:discountList.stats.inactive")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-600">
                  {stats.inactive}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>
                  {t("dashboard:discountList.stats.expired")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {stats.expired}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Controls */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div className="flex items-center gap-4">
              <Select
                value={filter}
                onChange={(e) => setFilter(e.target.value as FilterStatus)}
                className="w-48"
              >
                <option value="all">
                  {t("dashboard:discountList.filters.all")}
                </option>
                <option value="ACTIVE">
                  {t("dashboard:discountList.filters.active")}
                </option>
                <option value="INACTIVE">
                  {t("dashboard:discountList.filters.inactive")}
                </option>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleRefresh}
                variant="outline"
                disabled={loading}
              >
                {t("dashboard:discountList.actions.refresh")}
              </Button>
              <Button onClick={handleCreateNew}>
                {t("dashboard:discountList.actions.createNew")}
              </Button>
            </div>
          </div>

          {/* Content */}
          <Card>
            <CardContent className="p-0">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">
                      {t("dashboard:discountList.messages.loading")}
                    </p>
                  </div>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="text-red-600 mb-2">⚠️</div>
                    <p className="text-red-600 font-medium mb-2">{error}</p>
                    <Button onClick={handleRefresh} variant="outline" size="sm">
                      {t("dashboard:discountList.actions.refresh")}
                    </Button>
                  </div>
                </div>
              ) : filteredDiscounts.length === 0 ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="text-gray-400 mb-2">📋</div>
                    <p className="text-gray-600 font-medium mb-1">
                      {t("dashboard:discountList.table.noData")}
                    </p>
                    <p className="text-gray-500 text-sm mb-4">
                      {t("dashboard:discountList.table.noDataDescription")}
                    </p>
                    <Button onClick={handleCreateNew} size="sm">
                      {t("dashboard:discountList.actions.createNew")}
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  {/* Desktop Table View */}
                  <div className="hidden md:block overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>
                            {t("dashboard:discountList.table.headers.code")}
                          </TableHead>
                          <TableHead>
                            {t(
                              "dashboard:discountList.table.headers.percentage"
                            )}
                          </TableHead>
                          <TableHead>
                            {t("dashboard:discountList.table.headers.expiry")}
                          </TableHead>
                          <TableHead>
                            {t("dashboard:discountList.table.headers.status")}
                          </TableHead>
                          <TableHead>
                            {t("dashboard:discountList.table.headers.type")}
                          </TableHead>

                          <TableHead>
                            {t(
                              "dashboard:discountList.table.headers.description"
                            )}
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredDiscounts.map((discount) => (
                          <TableRow key={discount.id}>
                            <TableCell className="font-medium">
                              {discount.code}
                            </TableCell>
                            <TableCell>{discount.discount_percent}%</TableCell>
                            <TableCell>
                              <div
                                className={
                                  isExpired(discount.expire_date)
                                    ? "text-red-600"
                                    : ""
                                }
                              >
                                {formatDate(discount.expire_date)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <span
                                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  discount.state === "ACTIVE"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-gray-100 text-gray-800"
                                }`}
                              >
                                {t(
                                  `dashboard:discountList.table.status.${discount.state}`
                                )}
                              </span>
                            </TableCell>
                            <TableCell>
                              {t(
                                `dashboard:discountList.table.type.${discount.type}`
                              )}
                            </TableCell>
                            <TableCell className="max-w-xs truncate">
                              {discount.description || "-"}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Mobile Card View */}
                  <div className="md:hidden space-y-4 p-4">
                    {filteredDiscounts.map((discount) => (
                      <Card key={discount.id} className="p-4">
                        <div className="space-y-3">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-medium text-lg">
                                {discount.code}
                              </h3>
                              <p className="text-sm text-gray-600">
                                {discount.discount_percent}% تخفیف
                              </p>
                            </div>
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                discount.state === "ACTIVE"
                                  ? "bg-green-100 text-green-800"
                                  : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {t(
                                `dashboard:discountList.table.status.${discount.state}`
                              )}
                            </span>
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">
                                تاریخ انقضا:
                              </span>
                              <div
                                className={
                                  isExpired(discount.expire_date)
                                    ? "text-red-600"
                                    : ""
                                }
                              >
                                {formatDate(discount.expire_date)}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-500">نوع:</span>
                              <div>
                                {t(
                                  `dashboard:discountList.table.type.${discount.type}`
                                )}
                              </div>
                            </div>
                            {discount.description && (
                              <div className="col-span-2">
                                <span className="text-gray-500">توضیحات:</span>
                                <div className="mt-1">
                                  {discount.description}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}

export default function DiscountListPage() {
  return (
    <AuthGuard requireAdmin={true}>
      <DiscountListContent />
    </AuthGuard>
  );
}
