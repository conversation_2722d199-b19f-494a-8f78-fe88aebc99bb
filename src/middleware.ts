import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// Admin routes that require authentication
const adminRoutes = ["/dashboard", "/admin"];

// Public routes that don't require authentication
const publicRoutes = ["/", "/login", "/api/auth"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files, API routes (except auth session calls), and Next.js internals
  if (
    pathname.startsWith("/_next/") ||
    (pathname.startsWith("/api/") &&
      !pathname.startsWith("/api/auth/session")) ||
    pathname.includes(".") ||
    publicRoutes.some(
      (route) => pathname === route || pathname.startsWith(`${route}/`)
    )
  ) {
    return NextResponse.next();
  }

  // Check if the route requires admin authentication
  const isAdminRoute = adminRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`)
  );

  if (isAdminRoute) {
    // Check if admin guard is enabled
    const enableAdminGuard =
      process.env.NEXT_PUBLIC_ENABLE_ADMIN_GUARD === "true";
    if (!enableAdminGuard) {
      return NextResponse.next();
    }

    try {
      // Check for NextAuth session token in cookies
      const sessionToken =
        request.cookies.get("next-auth.session-token")?.value ||
        request.cookies.get("__Secure-next-auth.session-token")?.value;

      console.log("Middleware - Session token check:", {
        pathname,
        hasSessionToken: !!sessionToken,
        tokenPrefix: sessionToken
          ? sessionToken.substring(0, 10) + "..."
          : null,
      });

      // If no session token, redirect to login
      if (!sessionToken) {
        console.log(
          "Middleware - No session token found, redirecting to login"
        );
        const loginUrl = new URL("/login", request.url);
        loginUrl.searchParams.set("returnUrl", pathname);
        return NextResponse.redirect(loginUrl);
      }

      console.log("Middleware - Session token found, allowing access");

      // Check if admin role check is enabled
      const enableRoleCheck =
        process.env.NEXT_PUBLIC_ENABLE_ADMIN_ROLE_CHECK === "true";
      if (enableRoleCheck) {
        // In a production app, you'd decode the JWT token and check user roles
        // For now, we assume the presence of a valid session token means admin access
        // This can be enhanced to decode JWT and check specific claims
      }
    } catch (error) {
      console.error("Middleware auth error:", error);
      const loginUrl = new URL("/login", request.url);
      loginUrl.searchParams.set("returnUrl", pathname);
      return NextResponse.redirect(loginUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
