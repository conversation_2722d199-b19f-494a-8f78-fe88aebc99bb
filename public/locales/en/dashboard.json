{"title": "Admin Panel", "navigation": {"dashboard": "Dashboard", "userModeration": "User Moderation", "productManagement": "Product Management", "salesReports": "Sales Reports", "discountCodes": "Discount Codes", "generateDiscount": "Generate Discount"}, "cards": {"totalUsers": {"title": "Total Users", "description": "Active users in the system"}, "products": {"title": "Products", "description": "Items in catalog"}, "sales": {"title": "Sales", "description": "This month's revenue"}, "discounts": {"title": "Discounts", "description": "Active discount codes"}}, "discountGeneration": {"title": "Generate Discount", "subtitle": "Create a new discount code", "form": {"expireDate": {"label": "Expiry Date", "placeholder": "YYYY-MM-DD", "description": "Enter the expiry date for the discount code"}, "state": {"label": "Status", "placeholder": "Select discount code status", "options": {"ACTIVE": "Active", "INACTIVE": "Inactive"}}, "discountPercent": {"label": "Discount Percentage", "placeholder": "Enter discount percentage", "description": "Discount percentage between 1 and 100"}, "count": {"label": "Usage Count", "placeholder": "Number of times to use", "description": "Number of times this code can be used"}, "type": {"label": "Discount Type", "placeholder": "Select discount type", "options": {"simple": "Simple", "referral": "Referral"}}, "referralId": {"label": "Referral ID", "placeholder": "Enter referral ID", "description": "Required for referral discount codes"}, "description": {"label": "Description", "placeholder": "Enter discount code description", "description": "Description for the discount code"}}, "buttons": {"submit": "Create Discount Code", "cancel": "Cancel", "reset": "Clear Form"}, "validation": {"required": "This field is required", "invalidDate": "Invalid date format", "invalidPercent": "Discount percentage must be between 1 and 100", "invalidCount": "Usage count must be a positive number", "invalidReferralId": "Referral ID is required for referral discount codes"}, "messages": {"success": "Discount code created successfully", "error": "Error creating discount code", "loading": "Creating discount code..."}}}