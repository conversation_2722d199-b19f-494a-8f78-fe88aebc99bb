{"name": "admin-panel", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_OPTIONS=\"--inspect\" next dev --turbopack -p 7500", "build": "next build --turbopack", "start": "next start -p 7501", "lint": "eslint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-jalali": "^4.1.0-0", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.542.0", "next": "15.5.2", "next-auth": "^5.0.0-beta.29", "next-i18next": "^15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-i18next": "^15.7.3", "tailwind-merge": "^3.3.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "shadcn": "^3.1.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.8", "typescript": "^5"}}